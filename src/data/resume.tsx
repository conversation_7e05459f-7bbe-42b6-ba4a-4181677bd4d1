import { Icons } from "@/components/icons";
import { HomeIcon, NotebookIcon } from "lucide-react";

export const DATA = {
  name: "<PERSON><PERSON><PERSON>",
  initials: "DM",
  url: "https://devansh-portfolio.vercel.app",
  location: "Chennai, India",
  locationLink: "https://www.google.com/maps/place/chennai",
  description:
    "Platform Engineer | Full-Stack Developer | Cloud & DevOps Enthusiast",
  summary:
    "I'm a platform engineer passionate about building scalable backend systems and delivering seamless developer experiences. With a strong foundation in cloud technologies, DevOps practices, and full-stack development, I aim to solve real-world problems through elegant, cost-effective engineering solutions. Currently working at **Detect Technologies**, I've led the charge on cloud migrations, built dynamic storage systems, and enhanced CI/CD pipelines, all while contributing to frontend development with Angular.",
  avatarUrl: "/devansh.jpg",
  skills: [
    "Python",
    "Java",
    "JavaScript",
    "SQL",
    "Shell Scripting",
    "HTML/CSS",
    "Django",
    "Angular",
    "Flask",
    "A<PERSON>",
    "Docker",
    "Git",
    "Redis",
    "Node.js",
  ],
  navbar: [
    { href: "/", icon: HomeIcon, label: "Home" },
    { href: "/blog", icon: NotebookIcon, label: "Blog" },
  ],
  contact: {
    email: "<EMAIL>",
    tel: "+91-XXXXXXXXXX",
    social: {
      GitHub: {
        name: "GitHub",
        url: "https://github.com/devanshm96",
        icon: Icons.github,

        navbar: true,
      },
      LinkedIn: {
        name: "LinkedIn",
        url: "https://linkedin.com/in/devansh-m96",
        icon: Icons.linkedin,

        navbar: true,
      },
      email: {
        name: "Send Email",
        url: "mailto:<EMAIL>",
        icon: Icons.email,

        navbar: false,
      },
    },
  },

  work: [
    {
      company: "Detect Technologies",
      href: "https://detecttechnologies.com",
      badges: [],
      location: "Chennai, India",
      title: "Platform Engineer",
      logoUrl: "/detect.png",
      start: "Oct 2024",
      end: "Present",
      description:
        "Engineered a cloud-agnostic storage layer with dynamic Django model configurations, supporting AWS and MinIO integration. Led the cloud migration from AWS to MinIO, reducing infrastructure costs without compromising performance. Streamlined CI/CD processes for Django-based microservices, improving scalability and minimizing downtime. Contributed to frontend UX by building reusable Angular components (mat-tabs, mat-tables, mat-forms).",
    },
  ],
  education: [
    {
      school: "VIT Vellore",
      href: "https://vit.ac.in",
      degree: "B.Tech in Computer Science (Bioinformatics)",
      logoUrl: "/vit.png",
      start: "2020",
      end: "2024",
    },
  ],
  projects: [
    {
      title: "Ford Digital Backend Simulation",
      href: "#",
      dates: "2024",
      active: true,
      description:
        "Built a containerized microservice that streamed engine temperature data from vehicles to mobile devices in real time.",
      technologies: [
        "Docker",
        "Redis",
        "Flask",
        "Python",
        "Microservices",
      ],
      links: [],
      image: "",
      video: "",
    },
    {
      title: "Goldman Sachs Security Simulation",
      href: "#",
      dates: "2024",
      active: true,
      description:
        "Cracked over 300 password hashes, analyzed password weaknesses, and proposed improved policies.",
      technologies: [
        "Linux",
        "Hashcat",
        "Security Analysis",
        "Password Cracking",
      ],
      links: [],
      image: "",
      video: "",
    },
    {
      title: "Bubble Game",
      href: "https://devanshm96.github.io/BubbleGame-",
      dates: "2023",
      active: true,
      description:
        "Developed a dynamic browser-based game with real-time score tracking, hit detection, and game-over conditions.",
      technologies: [
        "HTML",
        "CSS",
        "JavaScript",
        "Game Development",
      ],
      links: [
        {
          type: "Website",
          href: "https://devanshm96.github.io/BubbleGame-",
          icon: <Icons.globe className="size-3" />,
        },
        {
          type: "Source",
          href: "https://github.com/devanshm96/BubbleGame-",
          icon: <Icons.github className="size-3" />,
        },
      ],
      image: "",
      video: "",
    },
  ],
  hackathons: [],
} as const;
